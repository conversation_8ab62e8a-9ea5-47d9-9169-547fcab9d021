-- <PERSON><PERSON><PERSON> to test the infinite agent commission fix
-- This script helps verify the issue and test the solution

-- 1. Check if user 019913e5-a460-7ef1-b10b-65187271b271 has any contract transactions
SELECT 
    'User Contract Activity Check' as check_type,
    COUNT(*) as contract_transaction_count,
    COALESCE(SUM(build_fee), 0) as total_build_fees
FROM hyper_liquid_transactions 
WHERE user_id = '019913e5-a460-7ef1-b10b-65187271b271'
    AND build_fee > 0;

-- 2. Check user's meme trading activity
SELECT 
    'User Meme Activity Check' as check_type,
    COUNT(*) as meme_transaction_count,
    COALESCE(SUM(platform_fee), 0) as total_platform_fees
FROM affiliate_transactions 
WHERE user_id = '019913e5-a460-7ef1-b10b-65187271b271';

-- 3. Check current infinite_agent_configs data for this user
SELECT 
    'Current Infinite Agent Config' as check_type,
    user_id,
    meme_total_fee_usd,
    meme_paid_commission_usd,
    meme_activity_cashback_usd,
    meme_net_fee_usd,
    contract_total_fee_usd,
    contract_paid_commission_usd,
    contract_net_fee_usd,
    final_commission_amount_usd
FROM infinite_agent_configs 
WHERE user_id = '019913e5-a460-7ef1-b10b-65187271b271';

-- 4. Check if user has a referral tree and who is in it
SELECT 
    'Referral Tree Analysis' as check_type,
    iat.infinite_agent_user_id,
    iat.root_user_id,
    iat.total_nodes,
    iat.max_depth,
    iat.direct_count
FROM infinite_agent_referral_trees iat
WHERE iat.infinite_agent_user_id = '019913e5-a460-7ef1-b10b-65187271b271'
    AND iat.status = 'ACTIVE';

-- 5. Check tree nodes to see who might have contract activity
WITH tree_info AS (
    SELECT id as tree_id
    FROM infinite_agent_referral_trees 
    WHERE infinite_agent_user_id = '019913e5-a460-7ef1-b10b-65187271b271'
        AND status = 'ACTIVE'
    LIMIT 1
),
tree_users AS (
    SELECT iatn.user_id
    FROM infinite_agent_tree_nodes iatn
    JOIN tree_info ti ON iatn.tree_id = ti.tree_id
)
SELECT 
    'Tree Users Contract Activity' as check_type,
    tu.user_id,
    COUNT(hlt.id) as contract_transactions,
    COALESCE(SUM(hlt.build_fee), 0) as total_build_fees
FROM tree_users tu
LEFT JOIN hyper_liquid_transactions hlt ON tu.user_id = hlt.user_id AND hlt.build_fee > 0
GROUP BY tu.user_id
ORDER BY total_build_fees DESC;

-- 6. Expected results after fix:
-- For meme-only users:
-- - contract_total_fee_usd should be 0
-- - contract_paid_commission_usd should be 0  
-- - contract_net_fee_usd should be 0
-- - Only meme fields should have values

-- 7. Test query to simulate the fix logic
SELECT 
    'Fix Simulation' as check_type,
    '019913e5-a460-7ef1-b10b-65187271b271' as user_id,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM hyper_liquid_transactions 
            WHERE user_id = '019913e5-a460-7ef1-b10b-65187271b271' 
                AND build_fee > 0
        ) THEN 'User has contract activity - use tree calculation'
        ELSE 'User has no contract activity - use user-only calculation'
    END as recommended_logic;
