#!/bin/bash

# Script to reseed activity tasks based on activity-cashback-requirement.md

set -e

echo "🔄 Starting Activity Tasks Reseed..."

# Check if we're in the right directory
if [ ! -f "go.mod" ]; then
    echo "❌ Error: Please run this script from the project root directory"
    exit 1
fi

# Check if config file exists
CONFIG_FILE="config.yaml"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ Error: Config file $CONFIG_FILE not found"
    exit 1
fi

echo "📋 Using config file: $CONFIG_FILE"

# Build and run the reseed command
echo "🏗️  Building reseed-tasks command..."
go build -o bin/reseed-tasks ./cmd/reseed-tasks

echo "🌱 Running task reseed..."
./bin/reseed-tasks -config="$CONFIG_FILE"

echo "✅ Activity tasks reseed completed successfully!"
echo ""
echo "📝 New tasks added based on activity-cashback-requirement.md:"
echo "   - Submit Market Research (Daily Task, 5 points)"
echo "   - Share Trading Screenshot (Community Task, 10 points)"
echo ""
echo "📝 Updated tasks:"
echo "   - Follow Twitter (updated link to XBITDEX_ZH)"
echo "   - Retweet/Like posts (changed to manual verification)"
echo "   - Join <PERSON><PERSON>ram (updated button text)"
echo ""
echo "🎯 All tasks are now aligned with the requirements document!"
