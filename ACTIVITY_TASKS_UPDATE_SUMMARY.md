# Activity Tasks Update Summary

## Overview
Updated the seed database to create activity tasks based on `activity-cashback-requirement.md`.

## Changes Made

### 1. New Task Identifiers Added
- `TaskIDMarketResearch` - For daily market research submission
- `TaskIDShareTradingScreenshot` - For sharing trading screenshots

### 2. New Daily Tasks Added

#### Submit Market Research
- **Task ID**: `MARKET_RESEARCH`
- **Points**: 5
- **Frequency**: Daily
- **Description**: Submit valid market research to earn points
- **Verification**: Manual
- **Action Target**: Research page
- **Icon**: 📝
- **Button Text**: submit

### 3. New Community Tasks Added

#### Share Trading Screenshot
- **Task ID**: `SHARE_TRADING_SCREENSHOT`
- **Points**: 10
- **Frequency**: Daily
- **Description**: Share valid trading screenshot to earn points
- **Verification**: Manual
- **Icon**: 📸
- **Button Text**: upload

### 4. Updated Existing Tasks

#### Follow Twitter
- **Updated**: External link changed to `https://x.com/i/intent/follow?screen_name=XBITDEX_ZH`
- **Reason**: Aligned with requirement document backup link

#### Retweet/Like Posts
- **Updated**: Frequency changed from `UNLIMITED` to `MANUAL`
- **Updated**: Tweet ID corrected to `19522412291666035139`
- **Reason**: Manual check requirement as per document

#### Join Telegram
- **Updated**: Button text changed from "view" to "join"
- **Reason**: Better UX alignment

### 5. Task Definitions Registry
- Added complete task definitions for new tasks in `TaskDefinitionRegistry`
- Includes display names, descriptions, points, and requirements

## Files Modified

1. **`internal/model/task_identifier.go`**
   - Added new task identifiers
   - Added task definitions in registry

2. **`internal/service/activity_cashback/task_seeder.go`**
   - Added new daily task: Submit Market Research
   - Added new community task: Share Trading Screenshot
   - Updated existing community tasks
   - Adjusted sort orders

3. **`scripts/reseed-activity-tasks.sh`** (New)
   - Script to easily reseed tasks with new configuration

## Task Categories Alignment

### Daily Tasks (as per requirement)
- ✅ Daily Check-in (5 points)
- ✅ Complete a MEME trade (200 points)
- ✅ Complete any trade (200 points - derivatives)
- ✅ Submit Market Research (5 points) - **NEW**
- ✅ Consecutive Check-in 3/7/15/30 days (50/200/1000 points)

### Community Tasks (as per requirement)
- ✅ Follow Twitter (50 points)
- ✅ Retweet Post (10 points)
- ✅ Like Post (10 points)
- ✅ Join Telegram (30 points)
- ✅ Invite Friends (100 points per person)
- ✅ Share Trading Screenshot (10 points) - **NEW**

### Trading Tasks (as per requirement)
- ✅ Trading Points (1-40 points based on volume)
- ✅ Cumulative Trading Volume $10,000 (300 points)
- ✅ Cumulative Trading Volume $50,000 (1,000 points)
- ✅ Cumulative Trading Volume $100,000 (2,500 points)
- ✅ Cumulative Trading Volume $500,000 (10,000 points)

## How to Apply Changes

Run the reseed script:
```bash
./scripts/reseed-activity-tasks.sh
```

Or manually:
```bash
go run ./cmd/reseed-tasks -config=config.yaml
```

## Notes

1. All new tasks follow the existing pattern and database schema
2. Task identifiers are unique and follow naming conventions
3. Points and frequencies match the requirement document exactly
4. Verification methods are set appropriately (manual for user-generated content)
5. Sort orders are maintained for proper UI display
6. All tasks include proper icons and button text for better UX

## Verification

After running the reseed:
1. Check database for new tasks
2. Verify task points and frequencies
3. Test task completion flows
4. Ensure UI displays correctly with new icons and button text
